"""Chart Service

This service handles chart generation from user queries using LLM analysis.
It processes natural language queries to create appropriate visualizations with real or mock data.
"""

import json
import logging
import random
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

from app.models.chart import (
    ChartType, ChartData, ChartDataPoint, ChartMetadata,
    ChartQueryRequest, ChartQueryResponse, ChartGenerationContext,
    ChartTypeRecommendation, MockDataGenerationConfig,
    DEFAULT_CHART_COLORS, CHART_TYPE_DESCRIPTIONS
)
from app.models.database import Database, DatabaseType
from app.services.database_manager_service import DatabaseManagerService
from app.services.database_service import DatabaseService
from app.utils.bedrock_client import BedrockClient
from app.config import settings

logger = logging.getLogger(__name__)


class ChartService:
    """Service for generating charts from natural language queries using LLM analysis."""

    def __init__(self):
        """Initialize the chart service with required dependencies."""
        self.bedrock_client = BedrockClient()
        self.mock_config = MockDataGenerationConfig()
        self.database_manager = DatabaseManagerService()
        self.database_service = DatabaseService()

    async def process_chart_query(self, request: ChartQueryRequest) -> ChartQueryResponse:
        """
        Process a chart query request and return a complete chart response.

        Args:
            request: The chart query request from the frontend

        Returns:
            ChartQueryResponse with chart data or error information
        """
        try:
            logger.info(f"Processing chart query: {request.prompt[:100]}...")

            # Create generation context
            context = ChartGenerationContext(
                user_query=request.prompt,
                user_id=request.user_id,
                dashboard_id=request.dashboard_id,
                processing_notes=[]
            )

            # Analyze query and recommend chart type
            recommendation = await self._get_chart_type_recommendation(context)
            context.recommended_chart_type = recommendation.chart_type
            context.detected_intent = recommendation.reasoning

            # Generate chart title from the user query
            title = self._generate_chart_title(context.user_query, recommendation.chart_type)

            # Generate data (real database data if database_id provided, otherwise mock data)
            if request.database_id and request.user_id:
                data_points, sql_query = await self._generate_real_chart_data(
                    context, recommendation.chart_type, request.database_id, request.user_id
                )
            else:
                data_points = await self._generate_mock_chart_data(context, recommendation.chart_type)
                sql_query = None

            # Create metadata and assemble final chart data
            metadata = self._create_chart_metadata(context, recommendation, sql_query)
            final_chart_type = context.recommended_chart_type

            chart_data = ChartData(
                title=title,
                chartType=final_chart_type,
                data=data_points,
                metadata=metadata
            )

            logger.info(f"Successfully generated {recommendation.chart_type} chart: {title}")
            return ChartQueryResponse(success=True, data=chart_data)

        except Exception as e:
            logger.error(f"Error processing chart query: {str(e)}")
            return ChartQueryResponse(
                success=False,
                error=f"Failed to generate chart: {str(e)}"
            )
    
    # ============================================================================
    # CHART TYPE RECOMMENDATION
    # ============================================================================

    async def _get_chart_type_recommendation(self, context: ChartGenerationContext) -> ChartTypeRecommendation:
        """
        Analyze the user query and recommend the most appropriate chart type.

        Args:
            context: Chart generation context

        Returns:
            ChartTypeRecommendation with the recommended chart type and reasoning
        """
        try:
            response = await self._get_llm_chart_recommendation(context)
            response_data = json.loads(response.strip())

            return ChartTypeRecommendation(
                chart_type=ChartType(response_data["chart_type"]),
                confidence=response_data["confidence"],
                reasoning=response_data["reasoning"],
                alternative_types=[ChartType(t) for t in response_data.get("alternative_types", [])]
            )

        except Exception as e:
            logger.warning(f"LLM chart type recommendation failed: {e}. Using fallback logic.")
            return self._fallback_chart_type_recommendation(context)

    async def _get_llm_chart_recommendation(self, context: ChartGenerationContext) -> str:
        """Get chart type recommendation from LLM."""
        system_prompt = """You are a data visualization expert. Analyze user queries and recommend the most appropriate chart type.

Available chart types and their use cases:
- table: Detailed data display with multiple columns and precise values
- line: Showing trends and changes over continuous time periods
- timebar: Data changes over specific time intervals and periods
- bar: Comparing discrete categories, showing changes over distinct time periods
- funnel: Conversion rates, process stages, and sequential data
- number: Single key metrics, KPIs, and summary statistics
- image: Visual content, photos, graphics, or visual documentation
- detail: Comprehensive information views, drill-down data, and detailed analysis
- text: Descriptive information, summaries, explanations, and narrative content
- pie: Parts of a whole, percentage breakdowns, and composition analysis
- activity: Events, actions, timelines, and activity patterns over time

Respond with a JSON object containing:
{
    "chart_type": "recommended_type",
    "confidence": 0.95,
    "reasoning": "explanation of why this chart type is best",
    "alternative_types": ["alternative1", "alternative2"]
}"""

        user_prompt = f"""Analyze this user query and recommend the best chart type:

Query: "{context.user_query}"

Consider:
1. What type of data comparison or analysis is being requested?
2. Is this about trends over time, categorical comparisons, or composition?
3. Does the user want to see detailed data or high-level insights?
4. Are there keywords that suggest specific visualization needs?

Provide your recommendation as a JSON object."""

        return await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.3
        )
    
    def _fallback_chart_type_recommendation(self, context: ChartGenerationContext) -> ChartTypeRecommendation:
        """
        Fallback chart type recommendation using keyword analysis.
        
        Args:
            context: Chart generation context
            
        Returns:
            ChartTypeRecommendation based on keyword analysis
        """
        query_lower = context.user_query.lower()
        
        # Keyword-based chart type detection
        if any(word in query_lower for word in ['breakdown', 'distribution', 'percentage', 'share', 'composition']):
            chart_type = ChartType.PIE
            reasoning = "Query suggests composition or percentage analysis, pie chart is most appropriate"
        elif any(word in query_lower for word in ['over time', 'trend', 'timeline', 'monthly', 'daily', 'yearly']):
            chart_type = ChartType.LINE
            reasoning = "Query indicates time-based trend analysis, line chart is most appropriate"
        elif any(word in query_lower for word in ['time periods', 'time intervals', 'duration', 'schedule']):
            chart_type = ChartType.TIMEBAR
            reasoning = "Query suggests time interval analysis, time bar chart is most appropriate"
        elif any(word in query_lower for word in ['compare', 'comparison', 'vs', 'versus', 'between']):
            chart_type = ChartType.BAR
            reasoning = "Query suggests categorical comparison, bar chart is most appropriate"
        elif any(word in query_lower for word in ['table', 'detailed', 'list', 'rows', 'columns']):
            chart_type = ChartType.TABLE
            reasoning = "Query suggests detailed data display, table is most appropriate"
        elif any(word in query_lower for word in ['conversion', 'funnel', 'stages', 'process', 'steps']):
            chart_type = ChartType.FUNNEL
            reasoning = "Query suggests process or conversion analysis, funnel chart is most appropriate"
        elif any(word in query_lower for word in ['count', 'number', 'total', 'metric']) and len(query_lower.split()) < 5:
            chart_type = ChartType.NUMBER
            reasoning = "Query asks for a simple metric, number display is most appropriate"
        elif any(word in query_lower for word in ['activity', 'events', 'actions', 'history', 'log']):
            chart_type = ChartType.ACTIVITY
            reasoning = "Query suggests activity or event tracking, activity chart is most appropriate"
        elif any(word in query_lower for word in ['image', 'photo', 'picture', 'visual', 'graphic']):
            chart_type = ChartType.IMAGE
            reasoning = "Query suggests visual content display, image display is most appropriate"
        elif any(word in query_lower for word in ['detail', 'details', 'drill down', 'comprehensive', 'analysis']):
            chart_type = ChartType.DETAIL
            reasoning = "Query suggests detailed analysis, detail display is most appropriate"
        elif any(word in query_lower for word in ['text', 'description', 'summary', 'explanation', 'narrative']):
            chart_type = ChartType.TEXT
            reasoning = "Query suggests text-based information, text display is most appropriate"
        else:
            chart_type = ChartType.BAR
            reasoning = "Default to bar chart for general data visualization"
        
        return ChartTypeRecommendation(
            chart_type=chart_type,
            confidence=0.7,
            reasoning=reasoning,
            alternative_types=[ChartType.BAR, ChartType.LINE, ChartType.TABLE]
        )
    
    def _generate_chart_title(self, user_query: str, chart_type: ChartType) -> str:
        """
        Generate an appropriate chart title based on the user query.

        Args:
            user_query: The user's natural language query
            chart_type: The recommended chart type (for future use)

        Returns:
            Generated chart title
        """
        # Clean and format the query as a title
        query_words = user_query.strip().split()

        # If query is short enough, use it directly
        if len(query_words) <= 8:
            title = user_query.strip()
            # Capitalize first letter if not already
            if title and not title[0].isupper():
                title = title[0].upper() + title[1:]
            return title

        # For longer queries, create a shortened version
        key_words = query_words[:6]
        title = ' '.join(key_words)

        # Add ellipsis if we truncated
        if len(query_words) > 6:
            title += "..."

        # Capitalize first letter
        if title and not title[0].isupper():
            title = title[0].upper() + title[1:]

        return title

    # ============================================================================
    # DATA GENERATION
    # ============================================================================

    async def _generate_real_chart_data(self, context: ChartGenerationContext, chart_type: ChartType, database_id: str, user_id: str) -> Tuple[List[ChartDataPoint], Optional[str]]:
        """
        Generate chart data by querying a real database.

        Args:
            context: Chart generation context
            chart_type: The type of chart to generate data for
            database_id: ID of the database to query
            user_id: ID of the user making the request

        Returns:
            Tuple of (List of chart data points, SQL query used)
        """
        try:
            # Get database schema and validate
            database_info = await self._get_database_info(user_id, database_id)
            if not database_info:
                return await self._generate_mock_chart_data(context, chart_type), None

            # Generate SQL query
            sql_query = await self._generate_sql_for_chart_with_schema(context.user_query, database_info, chart_type)
            if not sql_query:
                logger.warning("Failed to generate SQL query, falling back to mock data")
                return await self._generate_mock_chart_data(context, chart_type), None

            # Execute query and transform results
            query_result = await self._execute_database_query(database_id, user_id, sql_query)
            if query_result is None:
                return await self._generate_mock_chart_data(context, chart_type), None

            # Transform results to chart data points
            data_points = self._transform_query_result_to_chart_data(query_result, chart_type)

            # Post-process for single data points (convert to number chart if appropriate)
            self._post_process_single_data_point(context, data_points, chart_type)

            logger.info(f"Successfully generated chart data from database {database_id}")
            return data_points, sql_query

        except Exception as e:
            logger.error(f"Error generating real chart data: {e}")
            fallback_data = await self._generate_mock_chart_data(context, chart_type)
            return fallback_data, None

    async def _get_database_info(self, user_id: str, database_id: str) -> Optional[Dict[str, Any]]:
        """Get and validate database information."""
        try:
            schema_info = await self.database_manager.get_database_schema(user_id)
            logger.info(f"Retrieved schema info for user {user_id}: {len(schema_info.get('databases', []))} databases")

            if not schema_info.get("databases"):
                logger.info(f"User {user_id} has no connected databases")
                return None

            # Find the specific database in the schema
            for db in schema_info.get("databases", []):
                if db["id"] == database_id:
                    if not db.get("tables"):
                        logger.warning(f"Database {database_id} has no tables")
                        return None
                    logger.info(f"Found database {database_id} with {len(db.get('tables', []))} tables")
                    return db

            logger.warning(f"Database {database_id} not found for user {user_id}")
            return None

        except Exception as e:
            logger.error(f"Failed to get database schema for user {user_id}: {e}")
            return None

    async def _execute_database_query(self, database_id: str, user_id: str, sql_query: str) -> Any:
        """Execute SQL query on the database."""
        try:
            # Get database credentials
            database_obj = await self.database_manager.credential_service.get_credentials(user_id, database_id)
            if not database_obj:
                logger.error(f"Database {database_id} not found for user {user_id}")
                return None

            # Establish connection
            success, error = await self.database_service.connect_database(database_obj, user_id=user_id)
            if not success:
                logger.error(f"Failed to connect to database {database_id}: {error}")
                return None

            # Execute query
            return await self.database_service.execute_query(database_id, sql_query)

        except Exception as e:
            logger.error(f"Failed to execute SQL query on database {database_id}: {e}")
            return None

    def _post_process_single_data_point(self, context: ChartGenerationContext, data_points: List[ChartDataPoint], chart_type: ChartType) -> None:
        """Post-process single data points for better UX."""
        if (len(data_points) == 1 and
            chart_type not in [ChartType.NUMBER, ChartType.PIE] and
            self._should_convert_to_number_chart(context.user_query, data_points[0])):

            logger.info(f"Converting single data point from {chart_type} to NUMBER chart for better UX")
            context.recommended_chart_type = ChartType.NUMBER

    # ============================================================================
    # MOCK DATA GENERATION
    # ============================================================================

    async def _generate_mock_chart_data(self, context: ChartGenerationContext, chart_type: ChartType) -> List[ChartDataPoint]:
        """
        Generate appropriate mock data for the chart type.

        Args:
            context: Chart generation context
            chart_type: The type of chart to generate data for

        Returns:
            List of chart data points
        """
        try:
            if chart_type == ChartType.PIE:
                return self._generate_pie_chart_data(context)
            elif chart_type == ChartType.LINE:
                return self._generate_line_chart_data(context)
            elif chart_type == ChartType.BAR:
                return self._generate_bar_chart_data(context)
            elif chart_type == ChartType.TIMEBAR:
                return self._generate_timebar_chart_data(context)
            elif chart_type == ChartType.FUNNEL:
                return self._generate_funnel_chart_data(context)
            elif chart_type == ChartType.NUMBER:
                return self._generate_number_data(context)
            elif chart_type == ChartType.TABLE:
                return self._generate_table_data(context)
            elif chart_type == ChartType.ACTIVITY:
                return self._generate_activity_data(context)
            elif chart_type == ChartType.IMAGE:
                return self._generate_image_data(context)
            elif chart_type == ChartType.DETAIL:
                return self._generate_detail_data(context)
            elif chart_type == ChartType.TEXT:
                return self._generate_text_data(context)
            else:
                # Default to bar chart data
                return self._generate_bar_chart_data(context)
        except Exception as e:
            logger.error(f"Error generating chart data: {e}")
            return self._generate_default_data()

    def _generate_pie_chart_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for pie charts (parts of a whole)."""
        categories = self._get_relevant_categories(context.user_query)
        total = 100
        data_points = []

        for i, category in enumerate(categories):
            if i == len(categories) - 1:
                # Last category gets remaining percentage
                value = total
            else:
                value = random.randint(10, 40)
                total -= value

            data_points.append(ChartDataPoint(
                label=category,
                value=value,
                category="percentage"
            ))

        return data_points

    def _generate_line_chart_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for line charts (trends over time)."""
        time_labels = self._get_time_labels(context.user_query)
        base_value = random.randint(100, 1000)
        data_points = []

        for i, label in enumerate(time_labels):
            # Add some trend and randomness
            trend = i * random.randint(-10, 20)
            noise = random.randint(-50, 50)
            value = max(0, base_value + trend + noise)

            data_points.append(ChartDataPoint(
                label=label,
                value=value
            ))

        return data_points

    def _generate_bar_chart_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for bar charts (categorical comparisons)."""
        categories = self._get_relevant_categories(context.user_query)
        data_points = []

        for category in categories:
            value = random.randint(50, 500)
            data_points.append(ChartDataPoint(
                label=category,
                value=value
            ))

        return data_points

    def _generate_timebar_chart_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for time bar charts (time intervals)."""
        time_labels = self._get_time_labels(context.user_query)
        data_points = []

        for label in time_labels:
            value = random.randint(50, 300)
            data_points.append(ChartDataPoint(
                label=label,
                value=value
            ))

        return data_points

    def _generate_funnel_chart_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for funnel charts (conversion stages)."""
        stages = ["Initial", "Interested", "Qualified", "Proposal", "Closed"]
        initial_value = random.randint(1000, 5000)
        data_points = []

        current_value = initial_value
        for stage in stages:
            data_points.append(ChartDataPoint(label=stage, value=current_value))
            # Each stage has some drop-off
            current_value = int(current_value * random.uniform(0.6, 0.9))

        return data_points

    def _generate_number_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for number displays (single metric)."""
        value = random.randint(100, 10000)
        return [ChartDataPoint(label="Total", value=value)]

    def _generate_table_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for table displays (detailed data)."""
        categories = self._get_relevant_categories(context.user_query)
        data_points = []

        for category in categories:
            value = random.randint(50, 500)
            data_points.append(ChartDataPoint(
                label=category,
                value=value
            ))

        return data_points

    def _generate_activity_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for activity charts (events over time)."""
        time_labels = self._get_time_labels(context.user_query)
        data_points = []

        for label in time_labels:
            # Activity count varies more dramatically
            value = random.randint(5, 150)
            data_points.append(ChartDataPoint(
                label=label,
                value=value
            ))

        return data_points

    def _generate_image_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for image displays (visual content)."""
        return [ChartDataPoint(label="Image Content", value=1)]

    def _generate_detail_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for detail displays (comprehensive information)."""
        categories = self._get_relevant_categories(context.user_query)
        return [ChartDataPoint(label=category, value=random.randint(50, 500))
                for category in categories]

    def _generate_text_data(self, context: ChartGenerationContext) -> List[ChartDataPoint]:
        """Generate data for text displays (narrative content)."""
        return [ChartDataPoint(label="Text Content", value=1)]

    def _generate_default_data(self) -> List[ChartDataPoint]:
        """Generate default data when other methods fail."""
        return [
            ChartDataPoint(label="Category A", value=100),
            ChartDataPoint(label="Category B", value=150),
            ChartDataPoint(label="Category C", value=200),
        ]

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    def _get_relevant_categories(self, query: str) -> List[str]:
        """
        Extract or generate relevant categories based on the query.

        Args:
            query: User query string

        Returns:
            List of category labels
        """
        query_lower = query.lower()

        # Predefined category sets based on common query patterns
        if any(word in query_lower for word in ['product', 'item', 'goods']):
            return ["Product A", "Product B", "Product C", "Product D"]
        elif any(word in query_lower for word in ['department', 'team', 'division']):
            return ["Sales", "Marketing", "Engineering", "Support"]
        elif any(word in query_lower for word in ['region', 'location', 'geography']):
            return ["North", "South", "East", "West"]
        elif any(word in query_lower for word in ['channel', 'source', 'medium']):
            return ["Online", "Retail", "Partner", "Direct"]
        elif any(word in query_lower for word in ['age', 'demographic']):
            return ["18-25", "26-35", "36-45", "46+"]
        elif any(word in query_lower for word in ['priority', 'level', 'tier']):
            return ["High", "Medium", "Low"]
        else:
            return ["Category 1", "Category 2", "Category 3", "Category 4"]

    def _get_time_labels(self, query: str) -> List[str]:
        """
        Generate appropriate time labels based on the query.

        Args:
            query: User query string

        Returns:
            List of time period labels
        """
        query_lower = query.lower()

        if any(word in query_lower for word in ['daily', 'day', 'days']):
            base_date = datetime.now() - timedelta(days=6)
            return [(base_date + timedelta(days=i)).strftime("%m/%d") for i in range(7)]
        elif any(word in query_lower for word in ['weekly', 'week', 'weeks']):
            return [f"Week {i+1}" for i in range(8)]
        elif any(word in query_lower for word in ['monthly', 'month', 'months']):
            months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
            return months[:6]  # Last 6 months
        elif any(word in query_lower for word in ['yearly', 'year', 'years', 'annual']):
            current_year = datetime.now().year
            return [str(current_year - i) for i in range(4, -1, -1)]  # Last 5 years
        elif any(word in query_lower for word in ['quarterly', 'quarter', 'quarters']):
            return ["Q1", "Q2", "Q3", "Q4"]
        else:
            # Default to months
            return ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]

    def _create_chart_metadata(self, context: ChartGenerationContext, recommendation: ChartTypeRecommendation, sql_query: Optional[str] = None) -> ChartMetadata:
        """
        Create chart metadata based on context and recommendation.

        Args:
            context: Chart generation context
            recommendation: Chart type recommendation
            sql_query: Optional SQL query used to generate the data

        Returns:
            ChartMetadata object
        """
        # Generate axis labels based on chart type and query
        x_axis_label = self._generate_axis_label(context.user_query, "x", recommendation.chart_type)
        y_axis_label = self._generate_axis_label(context.user_query, "y", recommendation.chart_type)

        # Determine data source
        data_source = "Real Database Query" if sql_query else "Mock Data Generator"

        return ChartMetadata(
            xAxisLabel=x_axis_label,
            yAxisLabel=y_axis_label,
            colors=DEFAULT_CHART_COLORS[:6],  # Use first 6 colors
            sqlQuery=sql_query,
            description=recommendation.reasoning,
            dataSource=data_source,
            generatedAt=datetime.now().isoformat()
        )

    def _generate_axis_label(self, query: str, axis: str, chart_type: ChartType) -> Optional[str]:
        """
        Generate appropriate axis labels based on query and chart type.

        Args:
            query: User query
            axis: "x" or "y"
            chart_type: Type of chart

        Returns:
            Axis label or None
        """
        query_lower = query.lower()

        if chart_type in [ChartType.PIE, ChartType.NUMBER, ChartType.TEXT, ChartType.IMAGE, ChartType.DETAIL, ChartType.TABLE]:
            return None  # These chart types don't use axis labels

        if axis == "x":
            if any(word in query_lower for word in ['time', 'date', 'month', 'year', 'day']):
                return "Time Period"
            elif any(word in query_lower for word in ['category', 'type', 'group']):
                return "Category"
            else:
                return "Categories"
        else:  # y-axis
            if any(word in query_lower for word in ['count', 'number', 'total']):
                return "Count"
            elif any(word in query_lower for word in ['revenue', 'sales', 'amount', 'value']):
                return "Value"
            elif any(word in query_lower for word in ['percentage', 'percent', '%']):
                return "Percentage"
            else:
                return "Value"

    # ============================================================================
    # SQL GENERATION AND DATABASE OPERATIONS
    # ============================================================================

    async def _generate_sql_for_chart_with_schema(self, user_query: str, database_info: Dict[str, Any], chart_type: ChartType) -> Optional[str]:
        """
        Generate SQL query for chart data using LLM with actual database schema.

        Args:
            user_query: User's natural language query
            database_info: Database information including tables and columns
            chart_type: Type of chart being generated

        Returns:
            SQL query string or None if generation fails
        """
        try:
            # Build schema context from the database info
            schema_context = self._build_schema_context(database_info)

            # Create a comprehensive prompt with schema information
            system_prompt = f"""You are a SQL expert. Generate a {database_info['type']} SQL query to answer the user's question.

Database Schema:
{schema_context}

Requirements:
1. Return ONLY the SQL query, no explanations
2. Use proper {database_info['type']} syntax
3. For chart data, select 2 columns: one for labels (x-axis) and one for values (y-axis)
4. Use aggregate functions (COUNT, SUM, AVG) when appropriate
5. Order results by the value column in descending order
6. Handle NULL values appropriately
7. IMPORTANT: Create meaningful categorical breakdowns for visualization

Chart Type: {chart_type.value}
- For BAR/COLUMN charts: Group by meaningful categories (status, type, department, etc.) and count/sum
- For LINE charts: Group by time periods (date, month, year) and aggregate values
- For PIE charts: Group by categories and show distribution percentages

Examples of good queries for visualization:
- Instead of "SELECT COUNT(*) FROM users" use "SELECT status, COUNT(*) FROM users GROUP BY status"
- Instead of "SELECT SUM(amount) FROM orders" use "SELECT category, SUM(amount) FROM orders GROUP BY category"
- For time data: "SELECT DATE_TRUNC('month', created_at) as month, COUNT(*) FROM table GROUP BY month"

Always prefer GROUP BY queries that show breakdowns rather than single aggregate values.
"""

            user_prompt = f"User question: {user_query}\n\nGenerate the SQL query:"

            # Generate and clean SQL query
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.1
            )

            sql_query = self._clean_sql_response(response)

            if not self._validate_sql_query(sql_query):
                return None

            logger.info(f"Generated clean SQL query: {sql_query}")
            return sql_query

        except Exception as e:
            logger.error(f"Error generating SQL query with schema: {e}")
            return None

    def _build_schema_context(self, database_info: Dict[str, Any]) -> str:
        """Build a readable schema context from database info."""
        schema_lines = []
        schema_lines.append(f"Database: {database_info['name']} ({database_info['type']})")
        schema_lines.append("")

        for table in database_info.get('tables', []):
            table_name = table['name']
            columns = table.get('columns', [])

            schema_lines.append(f"Table: {table_name}")
            if columns:
                schema_lines.append("Columns:")
                for col in columns:
                    schema_lines.append(f"  - {col}")
            else:
                schema_lines.append("  (No column information available)")
            schema_lines.append("")

        return "\n".join(schema_lines)

    def _clean_sql_response(self, response: str) -> str:
        """Clean SQL response from LLM by removing markdown formatting."""
        sql_query = response.strip()

        # Remove markdown code blocks
        if sql_query.startswith('```sql'):
            sql_query = sql_query[6:]
        elif sql_query.startswith('```'):
            sql_query = sql_query[3:]

        if sql_query.endswith('```'):
            sql_query = sql_query[:-3]

        return sql_query.strip()

    def _validate_sql_query(self, sql_query: str) -> bool:
        """Validate that the SQL query looks correct."""
        if not sql_query or not sql_query.upper().startswith('SELECT'):
            logger.warning(f"Generated SQL doesn't look valid: {sql_query}")
            return False
        return True

    async def _generate_sql_for_chart(self, user_query: str, database_info: Dict[str, Any], chart_type: ChartType) -> Optional[str]:
        """
        Generate SQL query for chart data using LLM.

        Args:
            user_query: User's natural language query
            database_info: Information about the database
            chart_type: Type of chart being generated

        Returns:
            SQL query string or None if generation fails
        """
        try:
            # Create a simplified prompt for chart-specific SQL generation
            system_prompt = f"""You are a SQL expert. Generate a SQL query that returns data suitable for a {chart_type.value} chart.

For {chart_type.value} charts, the query should return:
- For bar/line/timebar charts: 2 columns (label, value)
- For pie charts: 2 columns (category, value)
- For number charts: 1 column (value)
- For funnel charts: 2 columns (stage, value)
- For table charts: multiple columns (flexible structure)
- For activity charts: 2 columns (time/event, value)

Keep the query simple and limit results to 20 rows maximum.
Return ONLY the SQL query, no explanations."""

            user_prompt = f"""Database: {database_info.get('name', 'Unknown')}
Database Type: {database_info.get('type', 'Unknown')}

User Question: {user_query}

Generate a SQL query that returns data for a {chart_type.value} chart."""

            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.2
            )

            # Clean the SQL response
            sql = response.strip()
            if sql.startswith("```sql"):
                sql = sql[6:]
            elif sql.startswith("```"):
                sql = sql[3:]
            if sql.endswith("```"):
                sql = sql[:-3]

            return sql.strip()

        except Exception as e:
            logger.error(f"Failed to generate SQL for chart: {e}")
            return None

    def _transform_query_result_to_chart_data(self, query_result: Any, chart_type: ChartType) -> List[ChartDataPoint]:
        """
        Transform database query results into chart data points.

        Args:
            query_result: Result from database query (DataFrame or list of dicts)
            chart_type: Type of chart being generated

        Returns:
            List of chart data points
        """
        try:
            logger.debug(f"Transforming query result: type={type(query_result)}, content={query_result}")

            # Convert result to DataFrame if it's not already
            if hasattr(query_result, 'to_dict'):
                # It's already a DataFrame
                df = query_result
                logger.debug(f"Using existing DataFrame with shape: {df.shape}")
            elif isinstance(query_result, list) and query_result:
                # Convert list of dicts to DataFrame
                import pandas as pd
                df = pd.DataFrame(query_result)
                logger.debug(f"Created DataFrame from list with shape: {df.shape}")
            else:
                logger.warning(f"Query result is empty or invalid format: {type(query_result)}")
                return self._generate_default_data()

            # Ensure we have data
            if df.empty:
                logger.warning("Query returned no data")
                return self._generate_default_data()

            logger.debug(f"DataFrame columns: {df.columns.tolist()}")
            logger.debug(f"DataFrame dtypes: {df.dtypes.to_dict()}")
            logger.debug(f"First few rows: {df.head().to_dict()}")

            data_points = []

            if chart_type == ChartType.NUMBER:
                # For number charts, take the first numeric value
                for col in df.columns:
                    if df[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                        value = float(df[col].iloc[0])
                        data_points.append(ChartDataPoint(label="Value", value=value))
                        logger.debug(f"Created number chart point: Value={value}")
                        break
            else:
                # For other chart types, expect label and value columns
                columns = df.columns.tolist()
                logger.debug(f"Processing {len(columns)} columns for chart type {chart_type}")

                if len(columns) >= 2:
                    label_col = columns[0]
                    value_col = columns[1]

                    # Find the first numeric column for values
                    for col in columns[1:]:
                        if df[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                            value_col = col
                            logger.debug(f"Using numeric column '{value_col}' for values")
                            break

                    # Convert to chart data points
                    for idx, row in df.head(20).iterrows():  # Limit to 20 points
                        try:
                            label = str(row[label_col])
                            value = float(row[value_col])
                            data_points.append(ChartDataPoint(label=label, value=value))
                            logger.debug(f"Created data point: {label}={value}")
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Skipping row {idx} due to conversion error: {e}")
                            continue
                elif len(columns) == 1:
                    # Single column - treat as values with row indices as labels
                    col = columns[0]
                    if df[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                        for idx, row in df.head(20).iterrows():
                            try:
                                label = f"Item {idx + 1}"
                                value = float(row[col])
                                data_points.append(ChartDataPoint(label=label, value=value))
                                logger.debug(f"Created single-column data point: {label}={value}")
                            except (ValueError, TypeError) as e:
                                logger.warning(f"Skipping row {idx} due to conversion error: {e}")
                                continue

            # If no valid data points were created, return default data
            if not data_points:
                logger.warning("No valid data points could be extracted from query result")
                logger.warning(f"DataFrame info: columns={df.columns.tolist()}, dtypes={df.dtypes.to_dict()}")
                return self._generate_default_data()

            logger.info(f"Successfully created {len(data_points)} data points from query result")
            return data_points

        except Exception as e:
            logger.error(f"Error transforming query result to chart data: {e}")
            logger.error(f"Query result type: {type(query_result)}")
            return self._generate_default_data()

    def _should_convert_to_number_chart(self, user_query: str, data_point: ChartDataPoint) -> bool:
        """
        Determine if a single data point should be displayed as a number chart instead of other chart types.

        Args:
            user_query: The original user query
            data_point: The single data point returned

        Returns:
            True if should convert to number chart, False otherwise
        """
        query_lower = user_query.lower()
        logger.info(f"Checking conversion for query: '{user_query}' with data point: {data_point}")

        # Keywords that suggest a single metric/number is expected
        number_keywords = [
            'total', 'count', 'sum', 'average', 'mean', 'median', 'max', 'min',
            'how many', 'how much', 'number of', 'amount of', 'size of',
            'revenue', 'profit', 'cost', 'price', 'value', 'score', 'rating',
            'percentage', 'percent', 'rate'
        ]

        # Check if query contains number-indicating keywords
        has_number_keywords = any(keyword in query_lower for keyword in number_keywords)
        logger.info(f"Query has number keywords: {has_number_keywords}")

        # Check if the data point label suggests it's a single metric
        label_lower = data_point.label.lower()
        single_metric_labels = ['total', 'count', 'sum', 'average', 'value', 'metric', 'score']
        has_metric_label = any(label in label_lower for label in single_metric_labels)
        logger.info(f"Data point label '{data_point.label}' has metric label: {has_metric_label}")

        # For now, let's be more aggressive and convert any single data point to number
        # This will help with the current case where "Show me users by role" returns one result
        result = has_number_keywords or has_metric_label or True  # Always convert single points for now
        logger.info(f"Final conversion decision: {result}")

        return result
