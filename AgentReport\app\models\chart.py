"""Chart Models

This module defines the chart-related models used for the chart selection API endpoint.
These models align with the frontend TypeScript interfaces for seamless integration.
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, validator


class ChartType(str, Enum):
    """Enumeration of supported chart types that match frontend TypeScript interface."""
    TABLE = "table"
    LINE = "line"
    TIMEBAR = "timebar"
    BAR = "bar"
    FUNNEL = "funnel"
    NUMBER = "number"
    IMAGE = "image"
    DETAIL = "detail"
    TEXT = "text"
    PIE = "pie"
    ACTIVITY = "activity"


class ChartDataPoint(BaseModel):
    """Model representing a single data point in a chart."""
    label: str = Field(..., description="The label for this data point")
    value: Union[int, float] = Field(..., description="The numeric value for this data point")
    category: Optional[str] = Field(None, description="Optional category for grouping")

    @validator('value')
    def validate_value(cls, v):
        """Ensure value is a valid number."""
        if not isinstance(v, (int, float)) or (isinstance(v, float) and not (v == v)):  # Check for NaN
            raise ValueError("Value must be a valid number")
        return v


class ChartMetadata(BaseModel):
    """Model representing chart metadata and configuration."""
    xAxisLabel: Optional[str] = Field(None, description="Label for the X-axis")
    yAxisLabel: Optional[str] = Field(None, description="Label for the Y-axis")
    colors: Optional[List[str]] = Field(None, description="Color palette for the chart")
    sqlQuery: Optional[str] = Field(None, description="SQL query used to generate the data")
    description: Optional[str] = Field(None, description="Additional description or context")
    dataSource: Optional[str] = Field(None, description="Source of the data")
    generatedAt: Optional[str] = Field(None, description="Timestamp when chart was generated")
    
    @validator('colors')
    def validate_colors(cls, v):
        """Validate color format (basic hex color validation)."""
        if v is not None:
            for color in v:
                if not isinstance(color, str) or not color.startswith('#'):
                    raise ValueError("Colors must be hex color strings starting with #")
        return v


class ChartData(BaseModel):
    """Model representing complete chart data structure."""
    title: str = Field(..., description="Title of the chart")
    chartType: ChartType = Field(..., description="Type of chart to render")
    data: List[ChartDataPoint] = Field(..., description="Array of data points for the chart")
    metadata: ChartMetadata = Field(default_factory=ChartMetadata, description="Chart metadata and configuration")

    @validator('data')
    def validate_data_not_empty(cls, v):
        """Ensure data array is not empty."""
        if not v:
            raise ValueError("Chart data cannot be empty")
        return v


class ChartQueryRequest(BaseModel):
    """Model for chart query requests from the frontend."""
    prompt: str = Field(..., min_length=1, max_length=4000, description="User query/prompt for chart generation")
    user_id: Optional[str] = Field(None, description="Optional user ID for personalization")
    dashboard_id: Optional[str] = Field(None, description="Optional dashboard ID for context")
    database_id: Optional[str] = Field(None, description="Optional database ID to query for real data")

    @validator('prompt')
    def validate_prompt(cls, v):
        """Validate and clean the prompt."""
        if not v or not v.strip():
            raise ValueError("Prompt cannot be empty")
        return v.strip()


class ChartQueryResponse(BaseModel):
    """Model for chart query responses to the frontend."""
    success: bool = Field(..., description="Whether the request was successful")
    data: Optional[ChartData] = Field(None, description="Chart data if successful")
    error: Optional[str] = Field(None, description="Error message if unsuccessful")
    
    @validator('data')
    def validate_success_data_consistency(cls, v, values):
        """Ensure data is present when success is True."""
        if values.get('success') and not v:
            raise ValueError("Data must be provided when success is True")
        return v
    
    @validator('error')
    def validate_error_success_consistency(cls, v, values):
        """Ensure error is present when success is False."""
        if not values.get('success') and not v:
            raise ValueError("Error message must be provided when success is False")
        return v


class ChartGenerationContext(BaseModel):
    """Internal model for chart generation context and LLM processing."""
    user_query: str = Field(..., description="Original user query")
    user_id: Optional[str] = Field(None, description="User ID for context")
    dashboard_id: Optional[str] = Field(None, description="Dashboard ID for context")
    detected_intent: Optional[str] = Field(None, description="Detected user intent from LLM")
    recommended_chart_type: Optional[ChartType] = Field(None, description="LLM recommended chart type")
    data_requirements: Optional[Dict[str, Any]] = Field(None, description="Data requirements identified by LLM")
    processing_notes: Optional[List[str]] = Field(None, description="Processing notes and decisions")


class ChartTypeRecommendation(BaseModel):
    """Model for LLM chart type recommendations."""
    chart_type: ChartType = Field(..., description="Recommended chart type")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0-1)")
    reasoning: str = Field(..., description="Explanation for the recommendation")
    alternative_types: Optional[List[ChartType]] = Field(None, description="Alternative chart types")


class MockDataGenerationConfig(BaseModel):
    """Configuration for mock data generation during development."""
    data_points_count: int = Field(default=5, ge=1, le=50, description="Number of data points to generate")
    value_range: tuple[int, int] = Field(default=(10, 1000), description="Range for generated values")
    use_realistic_labels: bool = Field(default=True, description="Whether to use realistic labels")
    include_categories: bool = Field(default=False, description="Whether to include categories")


# Default chart colors that match frontend constants
DEFAULT_CHART_COLORS = [
    '#8b5cf6',  # Purple
    '#06b6d4',  # Cyan
    '#10b981',  # Emerald
    '#f59e0b',  # Amber
    '#ef4444',  # Red
    '#8b5cf6',  # Purple variant
    '#06b6d4',  # Cyan variant
    '#10b981',  # Emerald variant
    '#f59e0b',  # Amber variant
    '#ef4444',  # Red variant
]


# Chart type to description mapping for LLM prompts
CHART_TYPE_DESCRIPTIONS = {
    ChartType.TABLE: "Tables are suitable for detailed data display with multiple columns and precise values",
    ChartType.LINE: "Line charts are perfect for showing trends and changes over continuous time periods",
    ChartType.TIMEBAR: "Time bar charts show data changes over specific time intervals and periods",
    ChartType.BAR: "Bar charts are ideal for comparing discrete categories or showing changes over time with distinct periods",
    ChartType.FUNNEL: "Funnel charts are ideal for showing conversion rates, process stages, and sequential data",
    ChartType.NUMBER: "Number displays are perfect for showing single key metrics, KPIs, and summary statistics",
    ChartType.IMAGE: "Image displays are used for showing visual content, photos, graphics, or visual documentation",
    ChartType.DETAIL: "Detail displays are used for comprehensive information views, drill-down data, and detailed analysis",
    ChartType.TEXT: "Text displays are used for descriptive information, summaries, explanations, and narrative content",
    ChartType.PIE: "Pie charts are best for showing parts of a whole, percentage breakdowns, and composition analysis",
    ChartType.ACTIVITY: "Activity charts show events, actions, timelines, and activity patterns over time",
}
